#!/usr/bin/env python3
"""
SOC Automation Suite
====================
A comprehensive security automation toolkit that integrates:
- Network reconnaissance and scanning
- File integrity monitoring
- Threat intelligence enrichment
- Automated reporting
- Task scheduling

Author: Security Team
Version: 1.0
"""

import os
import sys
import json
import time
import socket
import hashlib
import threading
import subprocess
import logging
from datetime import datetime
from pathlib import Path
import argparse

# Third-party libraries (install with: pip install requests schedule watchdog)
try:
    import requests
    import schedule
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
except ImportError as e:
    print(f"[ERROR] Missing dependencies: {e}")
    print("Install with: pip install requests schedule watchdog")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('soc_automation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class NetworkScanner:
    """Automated network reconnaissance and port scanning"""
    
    def __init__(self):
        self.results = []
    
    def ping_check(self, target):
        """Check if target is reachable"""
        try:
            if sys.platform.startswith('win'):
                cmd = ["ping", "-n", "3", target]
            else:
                cmd = ["ping", "-c", "3", target]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            return "0% packet loss" in result.stdout or "0% loss" in result.stdout
        except subprocess.TimeoutExpired:
            return False
        except Exception as e:
            logger.error(f"Ping check failed for {target}: {e}")
            return False
    
    def socket_scan(self, target, ports, timeout=1):
        """Fast socket-based port scan"""
        open_ports = []
        
        def scan_port(port):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(timeout)
                result = sock.connect_ex((target, port))
                if result == 0:
                    open_ports.append(port)
                    logger.info(f"[+] Port {port} open on {target}")
                sock.close()
            except Exception:
                pass
        
        # Threaded scanning for speed
        threads = []
        for port in ports:
            t = threading.Thread(target=scan_port, args=(port,))
            threads.append(t)
            t.start()
        
        for t in threads:
            t.join()
        
        return sorted(open_ports)
    
    def banner_grab(self, target, port, timeout=3):
        """Grab service banner"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            sock.connect((target, port))
            
            # Send HTTP request for web services
            if port in [80, 443, 8080, 8443]:
                sock.send(b"GET / HTTP/1.1\r\nHost: " + target.encode() + b"\r\n\r\n")
            
            banner = sock.recv(1024).decode('utf-8', errors='ignore').strip()
            sock.close()
            return banner[:200]  # Limit banner length
        except Exception:
            return None
    
    def nmap_scan(self, target, ports="22,80,443,8080"):
        """Execute Nmap scan if available"""
        try:
            cmd = ["nmap", "-Pn", "-sS", "-p", ports, target]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            return result.stdout
        except (subprocess.TimeoutExpired, FileNotFoundError):
            logger.warning("Nmap not available or timed out, using socket scan")
            return None
    
    def comprehensive_scan(self, target):
        """Perform comprehensive target assessment"""
        scan_result = {
            "target": target,
            "timestamp": datetime.now().isoformat(),
            "reachable": False,
            "open_ports": [],
            "banners": {},
            "nmap_output": None
        }
        
        logger.info(f"Starting scan of {target}")
        
        # Ping check
        scan_result["reachable"] = self.ping_check(target)
        if not scan_result["reachable"]:
            logger.warning(f"{target} appears unreachable")
            return scan_result
        
        # Port scan
        common_ports = [21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 3389, 5900, 8080]
        scan_result["open_ports"] = self.socket_scan(target, common_ports)
        
        # Banner grabbing
        for port in scan_result["open_ports"]:
            banner = self.banner_grab(target, port)
            if banner:
                scan_result["banners"][port] = banner
        
        # Nmap scan
        scan_result["nmap_output"] = self.nmap_scan(target)
        
        self.results.append(scan_result)
        return scan_result

class ThreatIntelligence:
    """Threat intelligence API integration"""
    
    def __init__(self):
        # Note: Add your API keys here
        self.virustotal_key = os.getenv('VT_API_KEY', '')
        self.abuseipdb_key = os.getenv('ABUSEIPDB_KEY', '')
    
    def check_ip_reputation(self, ip):
        """Check IP reputation using multiple sources"""
        reputation = {
            "ip": ip,
            "timestamp": datetime.now().isoformat(),
            "sources": {}
        }
        
        # IPInfo.io (free tier)
        try:
            response = requests.get(f"https://ipinfo.io/{ip}/json", timeout=10)
            if response.status_code == 200:
                data = response.json()
                reputation["sources"]["ipinfo"] = {
                    "city": data.get("city"),
                    "country": data.get("country"),
                    "org": data.get("org"),
                    "asn": data.get("asn")
                }
        except Exception as e:
            logger.error(f"IPInfo lookup failed for {ip}: {e}")
        
        # AbuseIPDB (if API key available)
        if self.abuseipdb_key:
            try:
                headers = {
                    'Key': self.abuseipdb_key,
                    'Accept': 'application/json'
                }
                params = {
                    'ipAddress': ip,
                    'maxAgeInDays': 90,
                    'verbose': ''
                }
                response = requests.get(
                    'https://api.abuseipdb.com/api/v2/check',
                    headers=headers,
                    params=params,
                    timeout=10
                )
                if response.status_code == 200:
                    data = response.json()
                    reputation["sources"]["abuseipdb"] = {
                        "abuse_confidence": data.get("data", {}).get("abuseConfidencePercentage", 0),
                        "is_public": data.get("data", {}).get("isPublic", False),
                        "total_reports": data.get("data", {}).get("totalReports", 0)
                    }
            except Exception as e:
                logger.error(f"AbuseIPDB lookup failed for {ip}: {e}")
        
        return reputation
    
    def check_domain_reputation(self, domain):
        """Check domain reputation"""
        try:
            result = subprocess.run(["whois", domain], capture_output=True, text=True, timeout=15)
            if result.returncode == 0:
                return {"domain": domain, "whois": result.stdout[:1000]}  # Limit output
        except Exception as e:
            logger.error(f"WHOIS lookup failed for {domain}: {e}")
        return None

class FileIntegrityMonitor:
    """File integrity monitoring and alerting"""
    
    def __init__(self, watch_path="/var/www"):
        self.watch_path = watch_path
        self.baseline_file = "file_baseline.json"
        self.baseline = {}
        self.observer = None
    
    def calculate_file_hash(self, filepath, algorithm="sha256"):
        """Calculate file hash"""
        try:
            hash_obj = getattr(hashlib, algorithm)()
            with open(filepath, "rb") as f:
                while chunk := f.read(65536):
                    hash_obj.update(chunk)
            return hash_obj.hexdigest()
        except Exception as e:
            logger.error(f"Error hashing {filepath}: {e}")
            return None
    
    def create_baseline(self, directory):
        """Create file integrity baseline"""
        logger.info(f"Creating baseline for {directory}")
        baseline = {}
        
        for root, dirs, files in os.walk(directory):
            for file in files:
                filepath = os.path.join(root, file)
                try:
                    stat = os.stat(filepath)
                    file_hash = self.calculate_file_hash(filepath)
                    if file_hash:
                        baseline[filepath] = {
                            "hash": file_hash,
                            "size": stat.st_size,
                            "modified": stat.st_mtime,
                            "created": stat.st_ctime
                        }
                except Exception as e:
                    logger.error(f"Error processing {filepath}: {e}")
        
        # Save baseline
        with open(self.baseline_file, "w") as f:
            json.dump(baseline, f, indent=2)
        
        self.baseline = baseline
        logger.info(f"Baseline created with {len(baseline)} files")
    
    def compare_baseline(self):
        """Compare current state to baseline"""
        if not os.path.exists(self.baseline_file):
            logger.error("No baseline found. Create one first.")
            return
        
        with open(self.baseline_file, "r") as f:
            baseline = json.load(f)
        
        changes = {
            "modified": [],
            "deleted": [],
            "added": [],
            "timestamp": datetime.now().isoformat()
        }
        
        # Check for modifications and deletions
        for filepath, baseline_info in baseline.items():
            if not os.path.exists(filepath):
                changes["deleted"].append(filepath)
                logger.warning(f"[DELETED] {filepath}")
            else:
                current_hash = self.calculate_file_hash(filepath)
                if current_hash and current_hash != baseline_info["hash"]:
                    changes["modified"].append(filepath)
                    logger.warning(f"[MODIFIED] {filepath}")
        
        # Check for new files
        for root, dirs, files in os.walk(self.watch_path):
            for file in files:
                filepath = os.path.join(root, file)
                if filepath not in baseline:
                    changes["added"].append(filepath)
                    logger.warning(f"[ADDED] {filepath}")
        
        return changes
    
    def start_realtime_monitoring(self):
        """Start real-time file monitoring"""
        class FSEventHandler(FileSystemEventHandler):
            def __init__(self, monitor):
                self.monitor = monitor
            
            def on_modified(self, event):
                if not event.is_directory:
                    logger.warning(f"[RT-MODIFIED] {event.src_path}")
            
            def on_created(self, event):
                if not event.is_directory:
                    logger.warning(f"[RT-CREATED] {event.src_path}")
                    # Alert on suspicious file types
                    if any(event.src_path.endswith(ext) for ext in ['.php', '.exe', '.sh', '.bat']):
                        logger.critical(f"[ALERT] Suspicious file created: {event.src_path}")
        
        self.observer = Observer()
        event_handler = FSEventHandler(self)
        self.observer.schedule(event_handler, self.watch_path, recursive=True)
        self.observer.start()
        logger.info(f"Real-time monitoring started for {self.watch_path}")
    
    def stop_realtime_monitoring(self):
        """Stop real-time monitoring"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            logger.info("Real-time monitoring stopped")

class ReportGenerator:
    """Generate comprehensive security reports"""
    
    def __init__(self):
        self.reports_dir = Path("reports")
        self.reports_dir.mkdir(exist_ok=True)
    
    def generate_scan_report(self, scan_results, output_format="json"):
        """Generate network scan report"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        report = {
            "report_type": "network_scan",
            "generated_at": datetime.now().isoformat(),
            "summary": {
                "targets_scanned": len(scan_results),
                "reachable_hosts": sum(1 for r in scan_results if r["reachable"]),
                "total_open_ports": sum(len(r["open_ports"]) for r in scan_results)
            },
            "results": scan_results
        }
        
        if output_format == "json":
            filename = self.reports_dir / f"scan_report_{timestamp}.json"
            with open(filename, "w") as f:
                json.dump(report, f, indent=2)
        elif output_format == "html":
            filename = self.reports_dir / f"scan_report_{timestamp}.html"
            self.generate_html_report(report, filename)
        
        logger.info(f"Scan report generated: {filename}")
        return filename
    
    def generate_html_report(self, data, filename):
        """Generate HTML report"""
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>SOC Automation Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; }}
                .summary {{ margin: 20px 0; padding: 15px; border: 1px solid #ccc; }}
                .critical {{ color: red; font-weight: bold; }}
                .warning {{ color: orange; }}
                .info {{ color: blue; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>SOC Automation Report</h1>
                <p>Generated: {data['generated_at']}</p>
                <p>Report Type: {data['report_type']}</p>
            </div>
            <div class="summary">
                <h2>Summary</h2>
                <p>Targets Scanned: {data['summary']['targets_scanned']}</p>
                <p>Reachable Hosts: {data['summary']['reachable_hosts']}</p>
                <p>Total Open Ports: {data['summary']['total_open_ports']}</p>
            </div>
            <div class="details">
                <h2>Detailed Results</h2>
                <table>
                    <tr>
                        <th>Target</th>
                        <th>Reachable</th>
                        <th>Open Ports</th>
                        <th>Services</th>
                    </tr>
        """
        
        for result in data['results']:
            ports_str = ', '.join(map(str, result['open_ports']))
            services = ', '.join(f"{port}:{banner[:50]}..." 
                               for port, banner in result['banners'].items())
            
            html_content += f"""
                    <tr>
                        <td>{result['target']}</td>
                        <td>{'✓' if result['reachable'] else '✗'}</td>
                        <td>{ports_str}</td>
                        <td>{services}</td>
                    </tr>
            """
        
        html_content += """
                </table>
            </div>
        </body>
        </html>
        """
        
        with open(filename, "w") as f:
            f.write(html_content)

class SOCAutomationSuite:
    """Main orchestrator for SOC automation tasks"""
    
    def __init__(self):
        self.scanner = NetworkScanner()
        self.threat_intel = ThreatIntelligence()
        self.file_monitor = FileIntegrityMonitor()
        self.reporter = ReportGenerator()
        self.running = False
    
    def scheduled_network_scan(self):
        """Scheduled network scanning task"""
        targets = ["*******", "*******", "scanme.nmap.org"]
        logger.info("Starting scheduled network scan")
        
        scan_results = []
        for target in targets:
            result = self.scanner.comprehensive_scan(target)
            scan_results.append(result)
            
            # Check threat intelligence for IPs
            if result["reachable"]:
                threat_data = self.threat_intel.check_ip_reputation(target)
                logger.info(f"Threat intel for {target}: {threat_data}")
        
        # Generate report
        self.reporter.generate_scan_report(scan_results, "json")
        self.reporter.generate_scan_report(scan_results, "html")
    
    def scheduled_integrity_check(self):
        """Scheduled file integrity check"""
        logger.info("Starting scheduled integrity check")
        changes = self.file_monitor.compare_baseline()
        if changes:
            total_changes = (len(changes["modified"]) + 
                           len(changes["deleted"]) + 
                           len(changes["added"]))
            if total_changes > 0:
                logger.critical(f"File integrity check found {total_changes} changes")
                
                # Save changes report
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                changes_file = f"reports/integrity_changes_{timestamp}.json"
                with open(changes_file, "w") as f:
                    json.dump(changes, f, indent=2)
    
    def start_monitoring(self):
        """Start continuous monitoring"""
        logger.info("Starting SOC Automation Suite")
        self.running = True
        
        # Schedule tasks
        schedule.every(4).hours.do(self.scheduled_network_scan)
        schedule.every(2).hours.do(self.scheduled_integrity_check)
        
        # Start real-time file monitoring
        self.file_monitor.start_realtime_monitoring()
        
        # Main loop
        try:
            while self.running:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
        except KeyboardInterrupt:
            logger.info("Shutting down...")
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """Stop all monitoring"""
        self.running = False
        self.file_monitor.stop_realtime_monitoring()
        logger.info("SOC Automation Suite stopped")

def main():
    parser = argparse.ArgumentParser(description="SOC Automation Suite")
    parser.add_argument("--scan", help="Scan a single target")
    parser.add_argument("--create-baseline", help="Create file integrity baseline for directory")
    parser.add_argument("--check-baseline", action="store_true", 
                       help="Check files against baseline")
    parser.add_argument("--monitor", action="store_true", 
                       help="Start continuous monitoring")
    parser.add_argument("--threat-intel", help="Check threat intelligence for IP")
    
    args = parser.parse_args()
    
    suite = SOCAutomationSuite()
    
    if args.scan:
        result = suite.scanner.comprehensive_scan(args.scan)
        print(json.dumps(result, indent=2))
    
    elif args.create_baseline:
        suite.file_monitor.create_baseline(args.create_baseline)
    
    elif args.check_baseline:
        changes = suite.file_monitor.compare_baseline()
        if changes:
            print(json.dumps(changes, indent=2))
    
    elif args.threat_intel:
        threat_data = suite.threat_intel.check_ip_reputation(args.threat_intel)
        print(json.dumps(threat_data, indent=2))
    
    elif args.monitor:
        suite.start_monitoring()
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main()